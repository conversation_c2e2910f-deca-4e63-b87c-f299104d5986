# Implementation Plan

- [x] 1. Set up project structure and core interfaces

  - Create `libs/codashi-core/src/skill-matching/` directory structure
  - Define TypeScript interfaces for `SkillMatchAnalysis`, `DirectSkillMatch`, `TransferableSkillMatch`
  - Create main function signature for `analyzeSkillMatch`
  - _Requirements: 3.1, 3.2_

- [x] 2. Implement skill consolidation logic

  - [x] 2.1 Create SkillConsolidator class with skill extraction from resume variations

    - Write function to extract skills from all resume sections (skills, work, projects)
    - Implement skill name normalization (lowercase, trim, etc.)
    - Create logic to merge duplicate skills across resume variations
    - _Requirements: 1.2, 1.5_

  - [x] 2.2 Add source tracking for introspection
    - Implement tracking of which resume variation contributed each skill
    - Create optional source resume information in consolidated skills
    - _Requirements: 1.5_

- [x] 3. Implement direct match analyzer

  - [x] 3.1 Create DirectMatchAnalyzer class with exact matching

    - Write exact string matching logic (case-insensitive)
    - Implement keyword matching between resume skills and job requirements
    - Create basic match result structure
    - _Requirements: 1.1, 1.3_

  - [x] 3.2 Add AI-powered synonym detection
    - <PERSON>reate <PERSON><PERSON><PERSON><PERSON> prompt template for synonym detection
    - Implement structured output parser for synonym analysis
    - Write batch processing for multiple skill comparisons
    - Integrate synonym detection into direct matching flow
    - _Requirements: 1.4_

- [x] 4. Implement transferable skill analyzer

  - [x] 4.1 Create TransferableSkillAnalyzer class with AI integration

    - Set up LangChain integration with BaseChatModel
    - Create prompt template for transferable skill analysis
    - Implement structured output parser with confidence ratings (1-3)
    - _Requirements: 2.1, 2.2, 2.3_

  - [x] 4.2 Add reasoning and confidence rating logic

    - Implement confidence rating assignment based on AI analysis
    - Add reasoning text extraction from AI responses
    - Create validation for confidence ratings and reasoning quality
    - _Requirements: 2.3, 2.4_

  - [x] 4.3 Optimize AI request batching
    - Implement efficient batching of skill comparison requests
    - Add deduplication logic to avoid redundant AI calls
    - Create request optimization for similar skill pairs
    - _Requirements: 5.2_

- [ ] 5. Create result aggregation and main function

  - [ ] 5.1 Implement result aggregation logic

    - Create summary statistics calculation (coverage percentage, match counts)
    - Implement result consolidation from direct and transferable matches
    - Add optional source resume information to final results
    - _Requirements: 3.2, 1.5_

  - [ ] 5.2 Implement main analyzeSkillMatch function
    - Wire together all components (consolidator, analyzers, aggregator)
    - Add input validation for resume variations and job data
    - Implement options handling for configuration parameters
    - Create proper async/await flow for AI operations
    - _Requirements: 3.1, 3.3_

- [ ] 6. Add error handling and graceful degradation

  - [ ] 6.1 Implement error handling classes

    - Create SkillMatchError class with error codes
    - Add partial results support for graceful degradation
    - Implement timeout handling for AI operations
    - _Requirements: 3.5, 4.4_

  - [ ] 6.2 Add fallback mechanisms
    - Implement fallback to direct matching when AI is unavailable
    - Create graceful handling of partial AI failures
    - Add meaningful error messages for different failure scenarios
    - _Requirements: 4.4_

- [ ] 7. Export functionality and update library index

  - Add exports to `libs/codashi-core/src/skill-matching/index.ts`
  - Update main library index file to expose skill matching functionality
  - Ensure proper TypeScript type exports for external usage
  - _Requirements: 3.1, 3.2_

- [ ] 8. Create minimal validation script
  - Create validation script in `libs/codashi-core/scripts/validate-skill-matching.ts`
  - Add sample resume variations and job data within the script
  - Write script to verify the function runs without errors and outputs expected structure
  - Add npm script entry to `libs/codashi-core/package.json` for easy invocation
  - _Requirements: Basic functionality validation_

**Note**: Comprehensive evaluation framework using LangChain evals will be implemented as a separate feature to avoid scope creep.
