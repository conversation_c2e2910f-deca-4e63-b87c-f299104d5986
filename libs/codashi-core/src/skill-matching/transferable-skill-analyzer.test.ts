import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { Job } from '../entities/job';
import { TransferableSkillAnalyzer } from './transferable-skill-analyzer';
import type { ConsolidatedSkill } from './types';

// Mock the LangChain dependencies
vi.mock('@langchain/core/prompts', () => ({
  ChatPromptTemplate: {
    fromTemplate: vi.fn().mockReturnValue({}),
  },
}));

vi.mock('@langchain/core/output_parsers', () => ({
  StructuredOutputParser: {
    fromZodSchema: vi.fn().mockReturnValue({
      getFormatInstructions: vi.fn().mockReturnValue('Format instructions'),
    }),
  },
}));

vi.mock('@langchain/core/runnables', () => ({
  RunnableSequence: {
    from: vi.fn(),
  },
}));

describe('TransferableSkillAnalyzer', () => {
  let analyzer: TransferableSkillAnalyzer;
  let mockModel: BaseChatModel;
  let mockChain: unknown;

  beforeEach(async () => {
    // Create mock chain
    mockChain = {
      invoke: vi.fn(),
    };

    // Mock RunnableSequence.from to return our mock chain
    const { RunnableSequence } = await import('@langchain/core/runnables');
    vi.mocked(RunnableSequence.from).mockReturnValue(mockChain);

    mockModel = {} as BaseChatModel;
    analyzer = new TransferableSkillAnalyzer(mockModel);
  });

  describe('analyzeTransferableSkills', () => {
    it('should return empty array when no unmatched resume skills', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [];
      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
      ];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should return empty array when no unmatched job skills', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];
      const unmatchedJobSkills: Job['skills'] = [];

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should analyze transferable skills and return matches', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [1],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Hooks'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Mock AI response
      mockChain.invoke.mockResolvedValue({
        matches: [
          {
            resumeSkill: 'Vue',
            jobSkill: 'React',
            confidenceRating: 2,
            reasoning:
              'Both are modern frontend frameworks with similar component-based architecture',
          },
          {
            resumeSkill: 'MySQL',
            jobSkill: 'PostgreSQL',
            confidenceRating: 3,
            reasoning: 'Both are relational databases with similar SQL syntax',
          },
        ],
      });

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        jobSkill: 'React',
        resumeSkill: 'Vue',
        confidenceRating: 2,
        reasoning:
          'Both are modern frontend frameworks with similar component-based architecture',
        sourceResume: undefined,
      });
      expect(result[1]).toEqual({
        jobSkill: 'PostgreSQL',
        resumeSkill: 'MySQL',
        confidenceRating: 3,
        reasoning: 'Both are relational databases with similar SQL syntax',
        sourceResume: undefined,
      });
    });

    it('should handle AI processing errors gracefully', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Mock AI error
      mockChain.invoke.mockRejectedValue(new Error('AI service unavailable'));

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toEqual([]);
    });

    it('should filter out matches with invalid confidence ratings', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Mock AI response with invalid confidence rating
      mockChain.invoke.mockResolvedValue({
        matches: [
          {
            resumeSkill: 'Vue',
            jobSkill: 'React',
            confidenceRating: 4, // Invalid - should be 1-3
            reasoning: 'Both are frontend frameworks',
          },
          {
            resumeSkill: 'MySQL',
            jobSkill: 'PostgreSQL',
            confidenceRating: 2,
            reasoning: 'Both are relational databases',
          },
        ],
      });

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should only include the valid match
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('MySQL');
      expect(result[0].confidenceRating).toBe(2);
    });

    it('should filter out matches with invalid reasoning', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] },
      ];

      // Mock AI response with invalid reasoning
      mockChain.invoke.mockResolvedValue({
        matches: [
          {
            resumeSkill: 'Vue',
            jobSkill: 'React',
            confidenceRating: 2,
            reasoning: 'similar', // Too generic/short
          },
          {
            resumeSkill: 'MySQL',
            jobSkill: 'PostgreSQL',
            confidenceRating: 3,
            reasoning:
              'Both are relational database management systems with similar SQL syntax and functionality',
          },
        ],
      });

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should only include the match with valid reasoning
      expect(result).toHaveLength(1);
      expect(result[0].resumeSkill).toBe('MySQL');
      expect(result[0].reasoning).toBe(
        'Both are relational database management systems with similar SQL syntax and functionality.'
      );
    });

    it('should clean and format reasoning text', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Mock AI response with reasoning that needs cleaning
      mockChain.invoke.mockResolvedValue({
        matches: [
          {
            resumeSkill: 'Vue',
            jobSkill: 'React',
            confidenceRating: 2,
            reasoning:
              '  both are modern frontend frameworks with component-based architecture  ', // Needs trimming and capitalization
          },
        ],
      });

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(1);
      expect(result[0].reasoning).toBe(
        'Both are modern frontend frameworks with component-based architecture.'
      );
    });

    it('should deduplicate similar skill comparisons', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js'],
          sourceResumes: [0],
        },
        {
          name: 'Vue.js',
          level: 'Advanced',
          keywords: ['Vue'],
          sourceResumes: [1],
        }, // Similar to above
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX'] },
      ];

      // Mock AI response - should only be called once due to deduplication
      mockChain.invoke.mockResolvedValue({
        matches: [
          {
            resumeSkill: 'Vue',
            jobSkill: 'React',
            confidenceRating: 2,
            reasoning: 'Both are frontend frameworks',
          },
        ],
      });

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      // Should only process one comparison due to deduplication
      expect(mockChain.invoke).toHaveBeenCalledTimes(1);
      expect(result).toHaveLength(1);
    });

    it('should optimize skill comparisons by priority', async () => {
      const unmatchedResumeSkills: ConsolidatedSkill[] = [
        {
          name: 'Vue',
          level: 'Intermediate',
          keywords: ['Vue.js', 'Components'],
          sourceResumes: [0],
        },
        {
          name: 'MySQL',
          level: 'Advanced',
          keywords: ['SQL', 'Database'],
          sourceResumes: [0],
        },
      ];

      const unmatchedJobSkills: Job['skills'] = [
        { name: 'React', level: null, keywords: ['JSX', 'Components'] }, // Has keyword overlap with Vue
        { name: 'PostgreSQL', level: null, keywords: ['SQL'] }, // Has keyword overlap with MySQL
      ];

      mockChain.invoke.mockResolvedValue({
        matches: [
          {
            resumeSkill: 'Vue',
            jobSkill: 'React',
            confidenceRating: 2,
            reasoning:
              'Both are frontend frameworks with component-based architecture',
          },
          {
            resumeSkill: 'MySQL',
            jobSkill: 'PostgreSQL',
            confidenceRating: 3,
            reasoning: 'Both are relational databases with SQL support',
          },
        ],
      });

      const result = await analyzer.analyzeTransferableSkills(
        unmatchedResumeSkills,
        unmatchedJobSkills
      );

      expect(result).toHaveLength(2);
      // Results should be sorted by confidence rating (highest first)
      expect(result[0].confidenceRating).toBe(3);
      expect(result[1].confidenceRating).toBe(2);
    });
  });
});
